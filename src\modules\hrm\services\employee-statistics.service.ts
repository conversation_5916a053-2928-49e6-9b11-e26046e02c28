/**
 * Service cho thống kê nhân viên
 */
import { apiClient } from '@/shared/api/axios';
import { ApiResponse } from '@/shared/types/api.types';

/**
 * Interface cho thống kê nhân viên
 */
export interface EmployeeStatistics {
  totalEmployees: number;
  totalUsers: number;
  activeEmployees: number;
  inactiveEmployees: number;
  newEmployeesThisMonth: number;
  employeesOnProbation: number;
  departmentDistribution: Array<{
    departmentName: string;
    employeeCount: number;
  }>;
  employmentTypeDistribution: Array<{
    type: string;
    count: number;
  }>;
  averageServiceYears: number;
  upcomingProbationEnds: number;
}

/**
 * Service cho thống kê nhân viên
 */
export class EmployeeStatisticsService {
  private static readonly BASE_URL = '/hrm/employees/statistics';

  /**
   * Lấy thống kê tổng quan nhân viên
   */
  static async getEmployeeStatistics(): Promise<ApiResponse<EmployeeStatistics>> {
    // Mock data for development
    const mockData: EmployeeStatistics = {
      totalEmployees: 125,
      totalUsers: 98,
      activeEmployees: 118,
      inactiveEmployees: 7,
      newEmployeesThisMonth: 8,
      employeesOnProbation: 12,
      departmentDistribution: [
        { departmentName: 'Công nghệ thông tin', employeeCount: 45 },
        { departmentName: 'Kinh doanh', employeeCount: 32 },
        { departmentName: 'Marketing', employeeCount: 18 },
        { departmentName: 'Nhân sự', employeeCount: 12 },
        { departmentName: 'Kế toán', employeeCount: 10 },
        { departmentName: 'Hành chính', employeeCount: 8 },
      ],
      employmentTypeDistribution: [
        { type: 'full_time', count: 95 },
        { type: 'part_time', count: 15 },
        { type: 'contract', count: 10 },
        { type: 'intern', count: 5 },
      ],
      averageServiceYears: 2.8,
      upcomingProbationEnds: 5,
    };

    return Promise.resolve({
      success: true,
      result: mockData,
      message: 'Success',
    });

    // Uncomment this when API is ready
    // const response = await apiClient.get<ApiResponse<EmployeeStatistics>>(this.BASE_URL);
    // return response.data;
  }

  /**
   * Lấy thống kê nhân viên theo khoảng thời gian
   */
  static async getEmployeeStatisticsByDateRange(
    startDate: string,
    endDate: string
  ): Promise<ApiResponse<EmployeeStatistics>> {
    const response = await apiClient.get<ApiResponse<EmployeeStatistics>>(
      `${this.BASE_URL}/date-range`,
      {
        params: { startDate, endDate },
      }
    );
    return response.data;
  }

  /**
   * Lấy thống kê nhân viên theo phòng ban
   */
  static async getEmployeeStatisticsByDepartment(
    departmentId: number
  ): Promise<ApiResponse<EmployeeStatistics>> {
    const response = await apiClient.get<ApiResponse<EmployeeStatistics>>(
      `${this.BASE_URL}/department/${departmentId}`
    );
    return response.data;
  }
}

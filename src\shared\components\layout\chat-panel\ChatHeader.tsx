import { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Avatar, IconCard } from '@/shared/components/common';

// Mock data for AI agents
const aiAgents = [
  {
    id: '1',
    name: 'Assistant',
    avatar: '/assets/images/ai-agents/assistant-robot.svg',
  }
];

interface ChatHeaderProps {
  onNewChat: () => void;
  onClose: () => void;
}

const ChatHeader = ({ onNewChat, onClose }: ChatHeaderProps) => {
  const { t } = useTranslation();
  const [selectedAgent,] = useState(aiAgents[0]);

  return (
    <div className="flex items-center justify-between p-3 bg-white dark:bg-dark">
      <div className="flex items-center">
        <div className="flex items-center cursor-pointer hover:bg-gray-100 dark:hover:bg-dark-lighter p-2 rounded">
              <Avatar src={selectedAgent.avatar} alt={selectedAgent.name} size="sm" />
              <span className="ml-2 font-medium">{selectedAgent.name}</span>
            </div>
      </div>

      <div className="flex items-center space-x-2">
        <IconCard icon="plus" variant="primary" onClick={onNewChat} title={t('chat.newChat')} />

        <IconCard
          icon="chevron-left"
          variant="default"
          onClick={onClose}
          title={t('common.close')}
        />
      </div>
    </div>
  );
};

export default ChatHeader;
